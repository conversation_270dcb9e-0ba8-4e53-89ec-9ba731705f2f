// fxiaoke_message.js

const DEBUG = true;
const API_CONFIG = {
    port: 27124,
    useHttps: true,
    apiKey: '690277fac30000e7d1048b31d746c60f19225f84565d2736777dfd6189ec6717',
    vault: 'Obsidian',
    folderPath: '工作/todo/企信'  
};

function log(message) {
    if (DEBUG) {
        console.log(`[Debug] ${message}`);
    }
}

// 获取当前日期作为文件名
function getDateFileName() {
    const now = new Date();
    return now.getFullYear() + '-' + 
           String(now.getMonth() + 1).padStart(2, '0') + '-' + 
           String(now.getDate()).padStart(2, '0');
}

// 格式化时间戳为可读时间
function formatTime(timestamp) {
    try {
        const date = new Date(Number(timestamp) * (timestamp.toString().length === 10 ? 1000 : 1));
        return `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
    } catch (e) {
        log(`Time format error: ${e.message}, timestamp: ${timestamp}`);
        return 'Invalid Time';
    }
}

// 处理消息内容
async function handleMessage() {
    try {
        if (!$response || !$response.body) {
            throw new Error('无响应数据');
        }

        log('开始解析响应数据');
        const body = JSON.parse($response.body);
        
        if (body.value && body.value.sessionList && body.value.sessionList.length > 0) {
            const fileName = getDateFileName();
            log(`处理文件: ${fileName}`);
            
            for (const session of body.value.sessionList) {
                if (session.lastMessageSummary) {
                    const sender = session.participantInfoList?.[0]?.name || session.lastMessageSenderName || '未知';
                    log(`发送者: ${sender}`);
                    
                    const messageContent = `- **${session.sessionName}** | ${sender} | ${formatTime(session.lastMessageTime)} | ${session.lastMessageSummary}\n`;
                    log(`消息内容: ${messageContent}`);
                    
                    try {
                        const result = await writeToObsidian(fileName, messageContent);
                        if (result) {
                            log('消息已写入 Obsidian');
                            backupToStore(fileName, messageContent);
                            
                            $notification.post(
                                '消息已保存',
                                sender,
                                session.lastMessageSummary
                            );
                        }
                    } catch (e) {
                        log(`写入失败: ${e.message}`);
                        backupToStore(fileName, messageContent);
                        $notification.post('写入 Obsidian 失败', fileName, e.message);
                    }
                }
            }
        } else {
            log('没有新消息需要处理');
        }
        
        $done({body: JSON.stringify(body)});
    } catch (err) {
        log(`脚本执行错误: ${err.message}`);
        $notification.post('纷享消息处理错误', '', err.message);
        $done({});
    }
}

async function writeToObsidian(fileName, content) {
    return new Promise((resolve, reject) => {
        const filePath = `工作/todo/企信/${fileName}.md`;
        
        log(`准备写入文件: ${filePath}`);
        log(`写入内容: ${content}`);
        
        // 使用消息内容的哈希作为唯一标识
        const messageKey = content.trim();
        const processedKey = `processed_${fileName}_${messageKey}`;
        
        // 检查是否已处理过这条消息
        if ($persistentStore.read(processedKey)) {
            log(`消息已处理过: ${messageKey}`);
            return resolve(false);
        }

        $httpClient.post({
            url: `https://127.0.0.1:${API_CONFIG.port}/vault/${encodeURIComponent(filePath)}`,
            headers: {
                'Authorization': `Bearer ${API_CONFIG.apiKey}`,
                'Content-Type': 'text/markdown',
            },
            body: content,
            insecure: true
        }, (error, response, data) => {
            if (error) {
                log(`API 请求错误: ${JSON.stringify(error)}`);
                reject(error);
            } else if (response.status !== 200 && response.status !== 204) {
                log(`API 响应状态: ${response.status}, ${data}`);
                reject(new Error(`API 响应错误: ${response.status}`));
            } else {
                // 记录已处理的消息
                $persistentStore.write('true', processedKey);
                log(`API 响应成功: ${response.status}, 写入路径: ${filePath}`);
                resolve(true);
            }
        });
    });
}


// 使用 persistentStore 作为备份存储
function backupToStore(fileName, content) {
    try {
        let existingContent = $persistentStore.read(fileName) || '';
        const success = $persistentStore.write(existingContent + content, fileName);
        log(`备份到 persistentStore ${success ? '成功' : '失败'}`);
        return success;
    } catch (e) {
        log(`备份存储错误: ${e.message}`);
        return false;
    }
}

// // 处理消息内容
// async function handleMessage() {
//     try {
//         if (!$response || !$response.body) {
//             throw new Error('无响应数据');
//         }

//         log('开始解析响应数据');
//         const body = JSON.parse($response.body);
        
//         if (body.value && body.value.sessionList && body.value.sessionList.length > 0) {
//             const fileName = getDateFileName();
//             log(`处理文件: ${fileName}`);
            
//             for (const session of body.value.sessionList) {
//                 if (session.lastMessageSummary) {
//                     const sender = session.participantInfoList?.[0]?.name || session.lastMessageSenderName || '未知';
//                     log(`发送者: ${sender}`);
                    
//                     // const messageContent = `${session.sessionName} | ${sender} | ${formatTime(session.lastMessageTime)} | ${session.lastMessageSummary}\n`;
//                     const messageContent = `- **${session.sessionName}** | ${sender} | ${formatTime(session.lastMessageTime)} | ${session.lastMessageSummary}\n`;
//                     log(`消息内容: ${messageContent}`);
                    
//                     try {
//                         // 尝试写入 Obsidian
//                         await writeToObsidian(fileName, messageContent);
//                         log('消息已写入 Obsidian');
                        
//                         // 同时备份到 persistentStore
//                         backupToStore(fileName, messageContent);
                        
//                         $notification.post(
//                             '消息已保存',
//                             sender,
//                             session.lastMessageSummary
//                         );
//                     } catch (e) {
//                         log(`写入失败: ${e.message}`);
//                         // 写入失败时仍然保存到 persistentStore
//                         backupToStore(fileName, messageContent);
//                         $notification.post('写入 Obsidian 失败', fileName, e.message);
//                     }
//                 }
//             }
//         } else {
//             log('没有新消息需要处理');
//         }
        
//         $done({body: JSON.stringify(body)});
//     } catch (err) {
//         log(`脚本执行错误: ${err.message}`);
//         $notification.post('纷享消息处理错误', '', err.message);
//         $done({});
//     }
// }

// 执行主函数
(() => {
    log('脚本开始执行');
    handleMessage().catch(err => {
        log(`异步执行错误: ${err.message}`);
        $done({});
    });
})();