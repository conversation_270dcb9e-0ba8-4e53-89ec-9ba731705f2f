[General]
# 日志等级：warning, notify, info, verbose (默认值：notify)
loglevel = warning
# 跳过某个域名或者 IP 段，这些目标主机将不会由 Surge Proxy 处理。(在 macOS
# 版本中，如果启用了 Set as System Proxy,  那么这些值会被写入到系统网络代理
# 设置中.)
skip-proxy = 127.0.0.1, ***********/16, 10.0.0.0/8, **********/12, **********/10, **********/10, ***********,localhost, *.local, *.firstshare.cn, *.foneshare.cn,*.ceshi112.com
# 强制使用特定的 DNS 服务器
bypass-tun = ***********/16, 10.0.0.0/8, **********/12
# 以下参数仅供 iOS 版本使用
# 将系统相关请求交给 Surge TUN 处理，并自动追加规则
# "IP-CIDR,********/8,DIRECT,no-resolve"
# bypass-system = true
# 将特定 IP 段跳过 Surge TUN，详见 Manual
replica = false
# 是否截取并保存 HTTP 流量 (启用后将对性能有较大影响) (默认值：false)
ipv6 = true
# 是否启动完整的 IPv6 支持 (默认值：false)
internet-test-url = http://www.apple.com/library/test/success.html
# 以下参数仅供 macOS 版本使用（多端口监听仅 Surge 3 支持）
proxy-test-url = http://www.apple.com/library/test/success.html
show-primary-interface-changed-notification = true
# 测速地址
; internet-test-url = http://cp.cloudflare.com/generate_204
; proxy-test-url = http://cp.cloudflare.com/generate_204
proxy-settings-interface = Primary Interface (Auto)
menu-bar-show-speed = false
# 其它
# external-controller-access = password@0.0.0.0:6170
allow-wifi-access = true
hide-crashlytics-request = true
http-api = gchr_surge@0.0.0.0:6171
http-api-tls = true
always-real-ip = *.srv.nintendo.net, *.stun.playstation.net, xbox.*.microsoft.com, *.xboxlive.com, *.fspage.com
geoip-maxmind-url = https://github.com/Hackl0us/GeoIP2-CN/raw/release/Country.mmdb
test-timeout = 20
http-api-web-dashboard = true
show-error-page-for-reject = true
read-etc-hosts = true
exclude-simple-hostnames = true
http-listen = 0.0.0.0
socks5-listen = 0.0.0.0
use-local-host-item-for-proxy = false
dns-server = *********, ***************, system
# encrypted-dns-server = quic://*********, quic://*********, https://**********/dns-query, https://************/dns-query

[Ponte]
server-proxy-name= "🇺🇸 dmit"

[Proxy]
Home = wireguard, section-name=Home
🇺🇸 bwg = snell, **************, 9443, psk=gchrpasswd222, version=5, reuse=true, tfo=true, shadow-tls-password=gchrpasswd222, shadow-tls-sni=www.microsoft.com, shadow-tls-version=3

🇭🇰HK = vmess, xboardhk.777021.xyz, 443, username=d4cb545a-7f5c-4860-9c26-fab52b1f2841, ws=true, ws-path=/8cb5544c-04b9-43f9-b6e0-7699d5269cca, ws-headers=Host:"xboardhk.777021.xyz", vmess-aead=true, tls=true, sni=xboardhk.777021.xyz
🇸🇬 SG = vmess, xboardsg.gchrs.ggff.net, 443, username=d4cb545a-7f5c-4860-9c26-fab52b1f2841, ws=true, ws-path=/b86a122a-6ad9-4dfe-bdd2-41f95bbc2f7e, ws-headers=Host:"xboardsg.gchrs.ggff.net", vmess-aead=true, tls=true, sni=xboardsg.gchrs.ggff.net
🇭🇰 华为香港1 = vmess, hwhk.068666.xyz, 443, username=6d9be832-55d5-45d2-bf4f-ce2188d3e9ee, ws=true, ws-path=/6d9be832-55d5-45d2-bf4f-ce2188d3e9ee, ws-headers=Host:"hwhk.068666.xyz", vmess-aead=true, tls=true, sni=hwhk.068666.xyz
🇭🇰 claw = vmess, claw.yylh.me, 443, username=31983375-be7e-4630-bbd7-e1631d820254, ws=true, ws-path=/31983375-be7e-4630-bbd7-e1631d820254, ws-headers=Host:"claw.yylh.me", vmess-aead=true, tls=true, sni=claw.yylh.me
🇭🇰 claw hy2 = hysteria2, *************, 43755, password=92b88857, download-bandwidth=1000, skip-cert-verify=true, sni=www.bing.com
🇯🇵 claw = vmess, clawjp.gchrs.ggff.net, 443, username=8fb57622-b35c-4c31-a334-6fad21c573db, ws=true, ws-path=/okgrvws, ws-headers=Host:"clawjp.gchrs.ggff.net", vmess-aead=true, tls=true, sni=clawjp.gchrs.ggff.net
🇸🇬 claw SG = vmess, hy2.gcru.me, 21852, username=1f2f528e-db93-4068-85b8-266770c15c7b, ws=true, ws-path=/tktl, ws-headers=Host:"hy2.gcru.me", vmess-aead=true, tls=true, sni=hy2.gcru.me
🇸🇬 claw SG hy2 = hysteria2, hy2.gcru.me, 38574, password=1f2f528e-db93-4068-85b8-266770c15c7b, download-bandwidth=1000, skip-cert-verify=true, sni=hy2.gcru.me, port-hopping=30000-31000
# 🇸🇬 claw SG hy2 = hysteria2, hy2.gcru.me, 38574, password=1f2f528e-db93-4068-85b8-266770c15c7b, download-bandwidth=1000, skip-cert-verify=true, sni=hy2.gcru.me, port-hopping=30000-31000
🇰🇷 AWS Seoul = vmess, awsseoul.gchrs.ggff.net, 50290, username=0b0d0ec1-1b30-4c26-95d9-6fef4e248bdd, ws=true, ws-path=/ray, ws-headers=Host:"awsseoul.gchrs.ggff.net", vmess-aead=true, tls=true, tfo=true, skip-cert-verify=true, sni=awsseoul.gchrs.ggff.net
🇯🇵 GCP hy2 = hysteria2, *************, 48840, password=52056bbf, download-bandwidth=1000, skip-cert-verify=true, sni=www.bing.com
# hysteria2://52056bbf@*************:48840/?insecure=1&sni=www.bing.com#Misaka-Hysteria2
GCP低流量 = vmess, gcpus.gchr.live, 443, username=170791c0-6c5f-4f1b-8b55-52ef63f83120, ws=true, ws-path=/170791c0-6c5f-4f1b-8b55-52ef63f83120, ws-headers=Host:"gcpus.gchr.live", vmess-aead=true, tls=true, sni=gcpus.gchr.live
cf-trojan = trojan, trojan.068666.xyz, 443, password=gchr2024, ws=true, ws-headers=Host:"trojan.068666.xyz", test-url=http://www.google.com/generate_204, sni=trojan.068666.xyz
🇭🇰 azure_hk = vmess, **************, 55198, username=40ad7ae7-1bc7-470b-b51b-a512dd867c6c, vmess-aead=true, tfo=true
🇭🇰 azure_hk_hy2 = hysteria2, **************, 61082, password=4945fba1, download-bandwidth=1000, skip-cert-verify=true, sni=www.bing.com
🇯🇵 GCP_JP = vmess, gcpjp.gchr.live, 443, username=81a1789a-2a50-4ffa-9319-b3454155943b, ws=true, ws-path=/81a1789a-2a50-4ffa-9319-b3454155943b, ws-headers=Host:"gcpjp.gchr.live", vmess-aead=true, tls=true, sni=gcpjp.gchr.live
🇺🇸 RackNerd DC02 hy2 NF GPT = hysteria2, *************, 39801, password=8116acb6, download-bandwidth=1000, skip-cert-verify=true, sni=www.bing.com
🇺🇸 RackNerd DC02 NF GPT = vmess, dc2.gchrs.ggff.net, 443, username=63c8d468-7013-4fef-a3a0-d7b0d2c020d7, ws=true, ws-path=/63c8d468-7013-4fef-a3a0-d7b0d2c020d7, ws-headers=Host:"dc2.gchrs.ggff.net", vmess-aead=true, tls=true, sni=dc2.gchrs.ggff.net
🇺🇸 dmit = trojan, dmit.068666.xyz, 51498, username=cyemt6vj, password=lm3EokjcKx, sni=dmit.068666.xyz
🇯🇵 FS_JP = vmess, fsjp.yylh.me, 443, username=a5e83c22-76ab-485a-b114-ec4906a27d60, ws=true, ws-path=/a00Ww5CQ1WlB6JHfIMsZ, ws-headers=Host:"fsjp.yylh.me", vmess-aead=true, tls=true, skip-cert-verify=true, sni=fsjp.yylh.me
🇸🇬 fssg = vmess, fssg.068666.xyz, 443, username=528e1788-0f24-4c6f-92a2-6f389e7bf87b, ws=true, ws-path=/, ws-headers=Host:"fssg.068666.xyz", vmess-aead=true, tls=true, sni=fssg.068666.xyz
🇩🇪 德国 NF GPT = vmess, nexus.gchrs.ggff.net, 443, username=627ae369-b384-48e1-af21-833a42ac3842, ws=true, ws-path=/627ae369-b384-48e1-af21-833a42ac3842, ws-headers=Host:"nexus.gchrs.ggff.net", vmess-aead=true, tls=true, sni=nexus.gchrs.ggff.net
🇩🇪 德国 NF GPT HY2 = hysteria2, **************, 22070, password=90eaac61, download-bandwidth=1000, skip-cert-verify=true, sni=www.bing.com
🇺🇸 bwg 副本 = snell, **************, 9443, psk=gchrpasswd222, version=4, reuse=true, tfo=true, shadow-tls-password=gchrpasswd222, shadow-tls-sni=www.microsoft.com, shadow-tls-version=3
🇺🇸 美国 webshare = socks5, ***********, 6486, username=mthwbwbc, password=nt5lpxznism9, underlying-proxy=🇺🇸 bwg

[Proxy Group]
🚀 节点选择 = select, fs_jp自动, "🐝 FS_JP", "🧑‍💻 🇺🇸 US Smart", "💰 🇺🇸 US Smart", "🧑‍💻 🇭🇰 HK Smart", "🧑‍💻 Asian Smart", "💰 Asian Smart", "✈️ 机场节点 Smart", "✈️ 52pokemon.cc 1109", "✈️ fac", "✈️ 机场节点", "✈️ 一", "🚀 linuxdo", "🇺🇸 免费节点", "🇭🇰 香港节点", "🇹🇼 台湾节点", "🇺🇲 美国节点", "🇯🇵 日本节点", "🇸🇬 狮城节点", "🇰🇷 韩国节点", "🎥 奈飞节点", include-all-proxies=1
🚀 手动切换 = select, "🚀 节点选择", "🧑‍💻 🇺🇸 US Smart", "🧑‍💻 US LB Persistent", "🧑‍💻 US LB", "💰 🇺🇸 US Smart", "🧑‍💻 Asian Smart", "✈️ fac", "✈️ 机场节点 Smart", "✈️ 机场节点", "✈️ 一", include-all-proxies=true, timeout=50
🇭🇰 香港节点 = select, DIRECT, include-all-proxies=true, policy-regex-filter=(🇭🇰)|(港)|(Hong)|(HK)|(hk), url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
🇹🇼 台湾节点 = select, DIRECT, include-all-proxies=true, timeout=20, policy-regex-filter=(🇨🇳)|(🇹🇼)|(台)|(Tai)|(TW), url=http://www.gstatic.com/generate_204, interval=300, tolerance=150, icon-url=https://raw.githubusercontent.com/lige47/QuanX-icon-rule/main/icon/taiwan(2).png
🇺🇲 美国节点 = select, DIRECT, include-all-proxies=true, timeout=20, policy-regex-filter=(🇺🇸)|(美)|(States)|(US), url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
🇺🇸美国 = select, "🇺🇲 美国节点", include-all-proxies=1
🇯🇵 日本节点 = select, DIRECT, include-all-proxies=true, timeout=20, policy-regex-filter=(🇯🇵)|(日)|(Japan)|(JP), url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
🇸🇬 狮城节点 = select, DIRECT, include-all-proxies=true, timeout=20, policy-regex-filter=(🇸🇬)|(新)|(Singapore)|(SG), url=http://www.gstatic.com/generate_204, interval=300, tolerance=150, icon-url=https://raw.githubusercontent.com/lige47/QuanX-icon-rule/main/icon/singapore(2).png
🇰🇷 韩国节点 = select, DIRECT, include-all-proxies=true, timeout=20, policy-regex-filter=(🇰🇷)|(韩)|(Korea)|(KR)|(대한민국), url=http://www.gstatic.com/generate_204, interval=300, tolerance=150, icon-url=https://raw.githubusercontent.com/lige47/QuanX-icon-rule/main/icon/Korea(1).png
🎥 奈飞节点 = select, DIRECT, include-all-proxies=true, timeout=20, policy-regex-filter=(🇭🇰)|(港)|(Hong)|(HK)|(hk)|(NF)|(奈飞)|(Netflix)|(video)|(Video)|(nf), url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
🇺🇸 免费节点 = select, DIRECT, include-all-proxies=true, timeout=20, policy-regex-filter=(cf), url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
🇺🇸 免费自动 = smart, include-all-proxies=true, timeout=20, policy-regex-filter=(cf), url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
📲 电报消息 = select, "🚀 节点选择", "🧑‍💻 🇺🇸 US Smart", "💰 🇺🇸 US Smart", "🧑‍💻 Asian Smart", "💰 Asian Smart", "✈️ 一", "♻️ 自动选择", "🇸🇬 狮城节点", "🇭🇰 香港节点", "🇹🇼 台湾节点", "🇯🇵 日本节点", "🇺🇲 美国节点", "🇰🇷 韩国节点", "🚀 手动切换", DIRECT
💬 OpenAi = select, "🚀 节点选择", "🇺🇸 dmit", "🇩🇪 德国 NF GPT", "🇩🇪 德国 NF GPT HY2", "🇺🇸 美国 webshare", "🇺🇲 美国节点", "✈️ 52pokemon.cc 1109", "✈️ 机场节点"
🍪 Claude = select, "🇺🇸 dmit", "🇩🇪 德国 NF GPT", "🇩🇪 德国 NF GPT HY2", "🇺🇸 bwg", "🇺🇲 美国节点"
🤖 Perplexity = select, DIRECT, "✈️ 机场节点", include-all-proxies=true, timeout=20, url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
💻 AugmentCode = select, DIRECT, "✈️ 机场节点", include-all-proxies=true, timeout=20, url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
🛍️ Amazon = select, "🚀 节点选择", "♻️ 自动选择", "🇸🇬 狮城节点", "🇭🇰 香港节点", "🇹🇼 台湾节点", "🇯🇵 日本节点", "🇺🇲 美国节点", "🇰🇷 韩国节点", "🚀 手动切换", DIRECT
🍎 苹果服务 = select, DIRECT, "🚀 节点选择", "🇺🇲 美国节点", "🇭🇰 香港节点", "🇹🇼 台湾节点", "🇸🇬 狮城节点", "🇯🇵 日本节点", "🇰🇷 韩国节点", "🚀 手动切换"
📹 油管视频 = select, cf-trojan, "✈️ 机场节点", "🇺🇸 免费节点", "🧑‍💻 🇺🇸 US Smart", "💰 🇺🇸 US Smart", "🧑‍💻 Asian Smart", "💰 Asian Smart", "✈️ 机场节点 Smart", "✈️ 一", "🚀 节点选择", "♻️ 自动选择", "🇸🇬 狮城节点", "🇭🇰 香港节点", "🇹🇼 台湾节点", "🇯🇵 日本节点", "🇺🇲 美国节点", "🇰🇷 韩国节点", "🚀 手动切换", DIRECT
Speedtest = select, cf-trojan, "🇺🇸 免费节点", "🧑‍💻 🇺🇸 US Smart", "💰 🇺🇸 US Smart", "🧑‍💻 Asian Smart", "💰 Asian Smart", "✈️ 机场节点 Smart", "✈️ 一", "🚀 节点选择", "♻️ 自动选择", "🇸🇬 狮城节点", "🇭🇰 香港节点", "🇹🇼 台湾节点", "🇯🇵 日本节点", "🇺🇲 美国节点", "🇰🇷 韩国节点", "🚀 手动切换", DIRECT
🎥 奈飞视频 = select, "🎥 奈飞节点", "🇸🇬 狮城节点", "🇺🇲 美国节点", "🇭🇰 香港节点", "🚀 节点选择", icon-url=https://raw.githubusercontent.com/lige47/QuanX-icon-rule/main/icon/netflix(old).png
🎥 迪士尼+ = select, include-all-proxies=true, timeout=20, policy-regex-filter=(🇺🇸)|(美)|(States)|(US)|(🇸🇬)|(新)|(Singapore)|(SG), url=http://www.gstatic.com/generate_204, interval=300, tolerance=150, icon-url=https://raw.githubusercontent.com/fmz200/wool_scripts/main/icons/apps/DisneyPlus.png
🎥 PrimeVideo = select, "🎥 奈飞节点", "🇸🇬 狮城节点", "🇺🇲 美国节点", "🇭🇰 香港节点", "🚀 节点选择"
📺 Infuse = select, DIRECT, "🚀 节点选择", "🇺🇲 美国节点", "🇭🇰 香港节点", "🇹🇼 台湾节点", "🇸🇬 狮城节点", "🇯🇵 日本节点", "🇰🇷 韩国节点", "🚀 手动切换"
📺 巴哈姆特 = select, "🇹🇼 台湾节点", "🚀 节点选择", "🚀 手动切换", DIRECT
📺 哔哩哔哩 = select, "🎯 全球直连", "🇹🇼 台湾节点", "🇭🇰 香港节点"
🌍 国外媒体 = select, "🧑‍💻 🇺🇸 US Smart", "💰 🇺🇸 US Smart", "🧑‍💻 Asian Smart", "💰 Asian Smart", "✈️ 一", "🚀 节点选择", "♻️ 自动选择", "🇭🇰 香港节点", "🇹🇼 台湾节点", "🇸🇬 狮城节点", "🇯🇵 日本节点", "🇺🇲 美国节点", "🇰🇷 韩国节点", "🚀 手动切换", DIRECT
🌏 国内媒体 = select, DIRECT, "🇭🇰 香港节点", "🇹🇼 台湾节点", "🇸🇬 狮城节点", "🇯🇵 日本节点", "🚀 手动切换"
🧑‍💻 🇺🇸 US Smart = smart, include-all-proxies=true, timeout=20, policy-regex-filter=(🇺🇸)|(美)|(States)|(US)
🧑‍💻 🇭🇰 HK Smart = smart, include-all-proxies=true, timeout=20, policy-regex-filter=^(?!.*azure_hk).*(🇭🇰|港|Hong|HK|hk)
🧑‍💻 US LB = smart, include-all-proxies=true, timeout=20, policy-regex-filter=(🇺🇸)|(美)|(States)|(US)
🧑‍💻 US LB Persistent = smart, include-all-proxies=true, persistent=true, timeout=20, policy-regex-filter=(🇺🇸)|(美)|(States)|(US)
💰 🇺🇸 US Smart = smart, policy-path=https://sub.gchrs.me/Cu9zjgaJnBQT84zAmF3s/download/collection/%E7%BB%84%E5%90%88?target=Surge, timeout=20000, policy-regex-filter=(🇺🇸)|(美)|(States)|(US)
🧑‍💻 Asian Smart = smart, include-all-proxies=true, timeout=20, policy-regex-filter=(🇨🇳)|(🇯🇵)|(🇸🇬)|(🇰🇷)|(🇹🇼)
💰 Asian Smart = smart, policy-path=https://sub.gchrs.me/Cu9zjgaJnBQT84zAmF3s/download/collection/%E7%BB%84%E5%90%88?target=Surge, timeout=2000, policy-regex-filter=(🇨🇳)|(台)|(Tai)|(TW)|(🇯🇵)|(日)|(Japan)|(JP)|(🇸🇬)|(🇰🇷)|(🇭🇰)|(港)|(Hong)|(HK)
✈️ 机场节点 Smart = smart, policy-path=https://sub.gchrs.me/Cu9zjgaJnBQT84zAmF3s/download/collection/%E7%BB%84%E5%90%88?target=Surge, timeout=2000, url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
✈️ 机场节点 = select, DIRECT, policy-path=https://sub.gchrs.me/Cu9zjgaJnBQT84zAmF3s/download/collection/%E7%BB%84%E5%90%88?target=Surge, timeout=2000, url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
🚀 linuxdo = select, DIRECT, policy-path=https://sub.gchrs.me/Cu9zjgaJnBQT84zAmF3s/download/linuxdo?target=Surge, timeout=2000, url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
# ✈️ 机场 v2b.guagua.info = select, DIRECT, policy-path=https://sub.store/download/v2b.guagua.info?target=Surge, timeout=2000, url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
# https://sub.gchrs.me/Cu9zjgaJnBQT84zAmF3s/download/linuxtest?target=Surge
# linuxtest = select, DIRECT, policy-path=https://fs.v2rayse.com/share/20250121/1rwyf74wew.yaml, timeout=2000, url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
# ✈️ guagua = select, DIRECT, policy-path=https://sub.gchrs.me/Cu9zjgaJnBQT84zAmF3s/download/v2b.guagua.info?target=Surge, timeout=2000, url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
✈️ 52pokemon.cc 1109 = select, DIRECT, policy-path=https://52pokemon.xz61.cn/api/v1/client/subscribe?token=f67a9e5119c64707cde9ad730e8d2789, timeout=2000, url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
✈️ fac = select, DIRECT, policy-path=https://sub.gchrs.me/Cu9zjgaJnBQT84zAmF3s/download/fac?target=Surge, timeout=2000, url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
# ✈️ 二 = select, policy-path=https://sub.store/download/%E8%B6%85%E7%BA%A7%E6%9C%BA%E5%9C%BA?target=Surge, url=http://www.gstatic.com/generate_204, test-url=http://www.gstatic.com/generate_204
✈️ 一 = select, policy-path=https://sub.gchrs.me/Cu9zjgaJnBQT84zAmF3s/download/free-50?target=Surge, url=http://www.gstatic.com/generate_204, test-url=http://www.gstatic.com/generate_204
# 🐝 FS_JP = url-test, policy-path=https://sub.gchrs.me/Cu9zjgaJnBQT84zAmF3s/download/fsjp?target=Surge, timeout=2000
🐝 FS_JP = select, policy-path=https://sub.gchrs.me/Cu9zjgaJnBQT84zAmF3s/download/fsjp?target=Surge, timeout=2000, url=http://www.gstatic.com/generate_204
♻️ 自动选择 = smart, include-all-proxies=true, timeout=50, url=http://www.gstatic.com/generate_204, interval=300, tolerance=50
📢 谷歌FCM = select, DIRECT, "🚀 节点选择", "🇺🇲 美国节点", "🇭🇰 香港节点", "🇹🇼 台湾节点", "🇸🇬 狮城节点", "🇯🇵 日本节点", "🇰🇷 韩国节点", "🚀 手动切换"
Ⓜ️ 微软云盘 = select, DIRECT, "🚀 节点选择", "🇺🇲 美国节点", "🇭🇰 香港节点", "🇹🇼 台湾节点", "🇸🇬 狮城节点", "🇯🇵 日本节点", "🇰🇷 韩国节点", "🚀 手动切换"
Ⓜ️ 微软服务 = select, DIRECT, "🚀 节点选择", "🇺🇲 美国节点", "🇭🇰 香港节点", "🇹🇼 台湾节点", "🇸🇬 狮城节点", "🇯🇵 日本节点", "🇰🇷 韩国节点", "🚀 手动切换"
🎮 游戏平台 = select, DIRECT, "🚀 节点选择", "🇺🇲 美国节点", "🇭🇰 香港节点", "🇹🇼 台湾节点", "🇸🇬 狮城节点", "🇯🇵 日本节点", "🇰🇷 韩国节点", "🚀 手动切换"
🎶 网易音乐 = select, DIRECT, "🚀 节点选择", "♻️ 自动选择"
🎯 全球直连 = select, DIRECT, "🚀 节点选择", "♻️ 自动选择"
🛑 广告拦截 = select, REJECT, "🚀 节点选择", DIRECT
🍃 应用净化 = select, REJECT, "🚀 节点选择", DIRECT
🐟 漏网之鱼 = select, "🧑‍💻 🇺🇸 US Smart", "💰 🇺🇸 US Smart", "🧑‍💻 Asian Smart", "💰 Asian Smart", "✈️ 一", "🚀 节点选择", "♻️ 自动选择", DIRECT, "🇺🇸 免费节点", "🇺🇸 免费自动", "🇭🇰 香港节点", "🇹🇼 台湾节点", "🇸🇬 狮城节点", "🇯🇵 日本节点", "🇺🇲 美国节点", "🇰🇷 韩国节点", "🚀 手动切换"
# > 外部节点自动匹配
# > 匹配到关键字，自动收纳为节点组

fs_jp自动 = smart, include-other-group=🐝 FS_JP

[Rule]
DOMAIN-SUFFIX,augmentcode.com,"💻 AugmentCode"
DOMAIN-SUFFIX,linux.do,"🇭🇰 claw"
DOMAIN-SUFFIX,linux.do,DIRECT
DOMAIN-SUFFIX,stripecdn.com,"💬 OpenAi"
DOMAIN-SUFFIX,linux.do,"🇭🇰 香港节点"
# DOMAIN-SUFFIX,xiaohongshu.com,"🇭🇰 香港节点"
PROCESS-NAME,/System/Applications/TV.app/Contents/MacOS/TV,"🚀 节点选择"
DOMAIN-SUFFIX,infini.money,"🇯🇵 日本节点"
DOMAIN-SUFFIX,sheerid.net,"💬 OpenAi"
DOMAIN-SUFFIX,featureassets.org,"💬 OpenAi"
DOMAIN-SUFFIX,grok.com,"💬 OpenAi"
DOMAIN-SUFFIX,macked.app,"🚀 节点选择"
DOMAIN-SUFFIX,google.com,"🚀 节点选择"
DOMAIN-SUFFIX,linux.do,DIRECT
DOMAIN-SUFFIX,perplexity.ai,"🤖 Perplexity"
PROCESS-NAME,/Applications/ChatGPT.app/Contents/MacOS/ChatGPT,"💬 OpenAi"
DOMAIN-SUFFIX,068666.xyz,"🚀 节点选择"
PROCESS-NAME,/Applications/Weixin.app/Contents/MacOS/Weixin,DIRECT
PROCESS-NAME,/Applications/Perplexity.app/Contents/MacOS/Perplexity,"🤖 Perplexity"
IP-CIDR,*************/24,Home,no-resolve
IP-CIDR,*************/32,DIRECT,no-resolve
DOMAIN-SUFFIX,labs.google,"🇺🇲 美国节点"
# RULE-SET,https://ruleset.skk.moe/List/non_ip/lan.conf,DIRECT
# RULE-SET,https://ruleset.skk.moe/List/ip/lan.conf,DIRECT

DOMAIN,cdn.usefathom.com,"🍪 Claude"
DOMAIN,servd-anthropic-website.b-cdn.net,"🍪 Claude"
DOMAIN-SUFFIX,anthropic.com,"🍪 Claude"
DOMAIN-SUFFIX,claude.ai,"🍪 Claude"
DOMAIN-SUFFIX,claudeusercontent.com,"🍪 Claude"

IP-CIDR,*************/32,DIRECT,no-resolve
DOMAIN,z.firstshare.cn,DIRECT
IP-CIDR,*************/32,DIRECT,no-resolve
IP-CIDR,**************/32,DIRECT,no-resolve
DOMAIN-SUFFIX,cursor.sh,"🇺🇲 美国节点"
DOMAIN-SUFFIX,google.com,"💬 OpenAi"
DOMAIN,aistudio.google.com,"💬 OpenAi"
DOMAIN-SUFFIX,ciciai.com,"🇸🇬 狮城节点"
DOMAIN-SUFFIX,www.googleapis.com,"💬 OpenAi"
DOMAIN-SUFFIX,labs.google,"💬 OpenAi"
DOMAIN-SUFFIX,monica.im,"💬 OpenAi"
PROCESS-NAME,/Applications/Claude.app/Contents/MacOS/Claude,"💬 OpenAi"
PROCESS-NAME,"/Applications/Claude.app/Contents/Frameworks/Claude Helper.app/Contents/MacOS/Claude Helper","💬 OpenAi"
DOMAIN-SUFFIX,anthropic.com,"💬 OpenAi"
DOMAIN-SUFFIX,886663.xyz,DIRECT
DOMAIN-SUFFIX,068666.xyz,DIRECT
DOMAIN-SUFFIX,groq.com,"💬 OpenAi"
DOMAIN-SUFFIX,x.ai,"💬 OpenAi"
DOMAIN-SUFFIX,claude.ai,"💬 OpenAi"
IP-CIDR,**************/32,DIRECT,no-resolve
DOMAIN-SUFFIX,jmsuper.com,"📺 Infuse"
DOMAIN-SUFFIX,amazon.eg,"🎥 PrimeVideo"
DOMAIN-SUFFIX,githubcopilot.com,"💬 OpenAi"
DOMAIN,webui.068666.xyz,DIRECT
DOMAIN-SUFFIX,oaifree.com,DIRECT
IP-CIDR,*************/32,DIRECT,no-resolve

PROCESS-NAME,/Applications/Spark.app/Contents/MacOS/Spark,"🚀 节点选择"
DOMAIN-SUFFIX,larksuite.com,"🚀 节点选择"
PROCESS-NAME,/Applications/Infuse.app/Contents/MacOS/Infuse,"📺 Infuse"
DOMAIN-SUFFIX,mypikpak.com,"🚀 节点选择"
IP-CIDR,**********/32,"🌍 国外媒体",no-resolve
DOMAIN-SUFFIX,tinyurl.com,"🚀 节点选择"
DOMAIN-SUFFIX,enjoy.bot,"🚀 节点选择"
# DOMAIN-SUFFIX,greencloudvps.com,DIRECT
# DOMAIN-SUFFIX,068666.xyz,DIRECT
DOMAIN-SUFFIX,mixpanel.com,"🚀 节点选择"
# PROCESS-NAME,/Applications/qbittorrent.app/Contents/MacOS/qbittorrent,"🚀 节点选择"
PROCESS-NAME,"/Applications/Screen Studio.app/Contents/MacOS/Screen Studio",REJECT
DOMAIN,order.luckydogsoft.com,REJECT
DOMAIN-SUFFIX,you.com,"🇺🇲 美国节点"
DOMAIN,rss.gchr.tech,DIRECT
# DOMAIN-SUFFIX,firstshare.cn,DIRECT
# DOMAIN,jump.foneshare.cn,DIRECT
DOMAIN-SUFFIX,foneshare.cn,DIRECT
# DOMAIN-SUFFIX,ceshi112.com,DIRECT
# DOMAIN-SUFFIX,ceshi113.com,DIRECT
DOMAIN-SUFFIX,fxiaoke.com,DIRECT
DOMAIN-SUFFIX,mypikpak.com,"🚀 节点选择"
DOMAIN,www.xmind.app,REJECT
DOMAIN,platform.deepseek.com,"🚀 节点选择"
IP-CIDR,**************/32,"🚀 节点选择",no-resolve
IP-CIDR,*************/32,DIRECT,no-resolve
IP-CIDR,************/32,"🚀 节点选择",no-resolve
IP-CIDR,**************/32,DIRECT,no-resolve
DOMAIN,sstats.adobe.com,REJECT
DOMAIN-SUFFIX,coze.com,"🇺🇲 美国节点"
DOMAIN,bard.google.com,"📢 谷歌FCM"
IP-CIDR,*************/32,DIRECT,no-resolve
DOMAIN-SUFFIX,perplexity.ai,"🤖 Perplexity"
IP-CIDR,*************/32,"🚀 节点选择",no-resolve
IP-CIDR,**************/32,DIRECT,no-resolve
# DOMAIN-SET,https://ruleset.skk.moe/List/domainset/reject.conf,REJECT
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/LocalAreaNetwork.list,"🎯 全球直连","update-interval=86400"
# RULE-SET,https://cdn.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Providers/LocalAreaNetwork.yaml,"🎯 全球直连","update-interval=86400"
# RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/UnBan.list,"🎯 全球直连","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanAD.list,"🛑 广告拦截","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanProgramAD.list,"🍃 应用净化","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/AmazonPrimeVideo/AmazonPrimeVideo.list,"🎥 PrimeVideo","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Amazon/Amazon.list,"🛍️ Amazon","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/GoogleFCM.list,"📢 谷歌FCM","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Speedtest/Speedtest.list,Speedtest,"update-interval=86400"
# RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/GoogleCN.list,"🎯 全球直连","update-interval=86400"
# RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/SteamCN.list,"🎯 全球直连","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/OneDrive.list,"Ⓜ️ 微软云盘","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Microsoft.list,"Ⓜ️ 微软服务","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Apple.list,"🍎 苹果服务","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Telegram.list,"📲 电报消息","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/OpenAi.list,"💬 OpenAi","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/SukkaW/Surge/refs/heads/master/Source/non_ip/ai.conf,"💬 OpenAi","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/NetEaseMusic.list,"🎶 网易音乐","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/Epic.list,"🎮 游戏平台","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/Origin.list,"🎮 游戏平台","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/Sony.list,"🎮 游戏平台","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/Steam.list,"🎮 游戏平台","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/Nintendo.list,"🎮 游戏平台","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/YouTube.list,"📹 油管视频","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/Netflix.list,"🎥 奈飞视频","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Disney/Disney.list,"🎥 迪士尼+","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/Bahamut.list,"📺 巴哈姆特","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/BilibiliHMT.list,"📺 哔哩哔哩","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/Bilibili.list,"📺 哔哩哔哩","update-interval=86400"
# RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ChinaMedia.list,"🌏 国内媒体","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ProxyMedia.list,"🌍 国外媒体","update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ProxyGFWlist.list,"🚀 节点选择","update-interval=86400"
# RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ChinaDomain.list,"🎯 全球直连","update-interval=86400"
# RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ChinaCompanyIp.list,"🎯 全球直连","update-interval=86400"
# RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Download.list,"🎯 全球直连","update-interval=86400"
IP-CIDR,**************/32,REJECT,no-resolve
IP-CIDR6,2402:4e00:1200:ed00:0:9089:6dac:96b6/128,REJECT,no-resolve
# > ZOL
DOMAIN,apppv.zol.com.cn,REJECT
DOMAIN,pvnapp.zol.com.cn,REJECT
PROCESS-NAME,"/Library/Application Support/Adobe/ARMDC/Application/Acrobat Update Helper.app/Contents/MacOS/Acrobat Update Helper",REJECT
# Rulesets
RULE-SET,SYSTEM,DIRECT
# LAN
RULE-SET,LAN,DIRECT
# GeoIP CN
DOMAIN,************,DIRECT
DOMAIN-SUFFIX,local,DIRECT
IP-CIDR,*********/8,DIRECT,no-resolve
IP-CIDR,**************/32,"🇭🇰 azure_hk",no-resolve
IP-CIDR,***************/32,DIRECT,no-resolve
SRC-IP,*************,DIRECT
GEOIP,CN,"🎯 全球直连"
# Final
FINAL,🐟 漏网之鱼

[Host]
# raw.githubusercontent.com = server:https://dns.cloudflare.com/dns-query
# github.com = server:https://dns.cloudflare.com/dns-query
# *.foneshare.cn = server:syslib
# *.firstshare.cn = server:syslib
# *.firstshare.cn = **************
# ceshi112.com = server:**************
dify.firstshare.cn = ************

[URL Rewrite]
^https?://(www.)?g.cn https://www.google.com 302
^https?://(www.)?google.cn https://www.google.com 302
https:\/\/backend.raycast.com http://*************:7000 header
# ^https:\/\/suzihaza\.com\/asset\/jquery\/slim-3\.2\.min\.js.* - reject
; https://www.fxiaoke.com/H/V5Messenger/UpdateSessionStatus
# ^https:\/\/www\.fxiaoke\.com\/H\/V5Messenger\/UpdateSessionStatus(?:\?.*)?$ - reject
# https://dcx.fxiaoke.com/monitor/uploadevent
^https:\/\/dcx\.fxiaoke\.com\/monitor\/uploadevent(?:\?.*)?$ - reject
# https://dcx.fxiaoke.com/monitor/canupload
^https:\/\/dcx\.fxiaoke\.com\/monitor\/canupload(?:\?.*)?$ - reject

; https://www.fxiaoke.com/H/V5Messenger/UpdateSessionStatus?_fs_token=E39ZCM8pE3KjC34mP2qqPcOmBM8tOZCjOcPaCpSnOZ4oDZGq&traceId=O-E.fs.7703-33926375

# https:\/\/*.fxiaoke.com\/H\/V5Messenger\/UpdateSessionStatus https://www.fxiaoke.com/H/V5Messenger/UpdateSessionStatus 302

[MITM]
skip-server-cert-verify = true
tcp-connection = true
h2 = true
# ca-passphrase = 38E12CA1
# ca-p12 = 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
# *.lemonsqueezy.com, *.paddleapi.com, *.cloudflareclient.com,*.elpass.app, *.akamaized.net, *.scdn.co, *.scdn.co, *.scdn.co,buy.itunes.apple.com, *.surfshark.com,
hostname = api.revenuecat.com, firestore.googleapis.com, *.raycast.com, api.lemonsqueezy.com, v3.paddleapi.com, api.gumroad.com, *.fxiaoke.com, api.cursor.sh, datiapi.duomiao.pro
hostname-disabled = music.163.com, firestore.googleapis.com, buy.itunes.apple.com, api.gumroad.com, v3.paddleapi.com, api.lemonsqueezy.com, *.raycast.com, *.fxiaoke.com, api.revenuecat.com, api.cursor.sh
ca-passphrase = 7ECD76A7
ca-p12 = MIIKPAIBAzCCCgYGCSqGSIb3DQEHAaCCCfcEggnzMIIJ7zCCBF8GCSqGSIb3DQEHBqCCBFAwggRMAgEAMIIERQYJKoZIhvcNAQcBMBwGCiqGSIb3DQEMAQYwDgQIeBrRpAtfaGgCAggAgIIEGMgulvoYbKcRpDmhHwzMrPr9k3MrkSUmzX4OiAx9WYD11xwNUVV57/c+WnmXAuiU4Q+Xp3iqqqL9wh+r0AbUZZS9pqOneE0x/ATg+vdPyjPXzn2BBubVPOr0Rb+e3r5385Ai7FESKwUy+YP29A3Mkq7z5xmZsFExXkVwPnncwQZMUlMtiZMaxTrhyxyGlR039X3ouv30ruOf8JnymtOKdDOEcwkz+UjAMwUC9/3lK+6Wl5qTZQeZ7UcIfB91LDW58roa96pRp1N8LokIGpvcDUinm8dc6vE1zjabqi0NVB7MFRebOrL/Apx5wQ5pqc44sRHSV8VR+C24582MJJ5tu2kCTRvNRi8MdsiPIO8YxlMZKZeSe2FkXgOyywRKm+Qx1ypXWBxRAOSIUlr8Ro2Om0db2rvr8iPnfptlfkSkFJkyf5Age7KWVXW23qN9z5ybiLSFbHpCHuJ0k6+jfKR378mIxTTjLyJSDtbt8NslnAZHIPiJOPK4zZW+VfEUmhOj6zIGZA+N5XYU1I1xew73jdKrCtOV1bptUq6Ntl4bzQqW6TXhVWTfiJZlVkRdEzIwUP8ymZB6Oo1m/uNuCzifI4Kh60x41LKRwmsud96H5Xg9jIDIr8f/uvZXCgI54h+w+tcmmN43MgxL4Qgx/sgr7lrl/MAL3PXOsfYXjC9+Rmde/sAwV58CWaZ2fpRpOEpTrDZiTHiiNzU7ZJndBpZ6ipPNbnQVdto8qiR/NmCU3KUoFYJ6HsfINxMQrWhsKWLkLyXEpY37XBwjMMzL5P5ve8S4EkeYgqOqnqfa50wUJTiXN+tuhjgM/nD7wCzApenh/N+YECcL5V3LRXXWI0+sKXzv4wc6+X03Cs6asWRzIbFP5ExzKNKM+f90TRcchkGNzoCCSmJCKdKbzGn9xuGZhHIeN+eP5mddmc+6+vxvJSgCCgUuSIO1L94SVG2urI2mJVn4Sm9uET/0VmBcHc8w3UOHOmmZ1jlx74sPuzb6EZFt+HHxfNiLAsht/Zea6/+dr+1zpHKjIjEphm08lmuJIZuhS842bevfVwwlqEUfdOOFBays3/eaW2vIPPA47zuVyLUeE367Y6oSJc0SLVXIxNZIBPg5l4FP3tK79KhkpAL7KlIIlNej+JRYKAZRPY3j6c3oeAAe95gViY42+1/SwuSCmt4YLqtiEjZGok7yFmQStE/VPprnyv3Uy5NAFutrQasLTzSSG5Tfz4pPflHyOadkGwkDpGUUWIGKG+aF2sRsgKw3mXZK8a5qOw9UaXPdr8qzKi8zaYxQCmmkccbfb+iEZhKhy0Ux88c4rBTUYKmtC93743PgsSsO9j+4uVGDROj+IL2u267+XP65gG8vKsQv36cBUyMrFHOeY2qG81NGKs34tzcUB9wwggWIBgkqhkiG9w0BBwGgggV5BIIFdTCCBXEwggVtBgsqhkiG9w0BDAoBAqCCBO4wggTqMBwGCiqGSIb3DQEMAQMwDgQI8o8xUXoDDV8CAggABIIEyCGawmR33i3KF62ijvdjV07S99bZeRwZ1nsqZMA2WjE8h0fgTHoqkRy+kG2X3SIRUvmllGCGDh47zGsH2rBsyVsVMp/fMr0XuGhzP0eWnCL6AFvC5sXcaaOfialRzJhpsc3Q/e1ctjM8Y7FDD4llqcSNX6unmYm9OWlLGhM9M8U4MFtscXZZBQGdkGqLRXgGb3JsatkaGB2dTPTwRbKIX8agCUxEat15TsthSUq2Hwhb0Ss2BdXe6ZbSAtlfTPQ1Riwg4FIN7fqxktkvOanILUSnCsH00YQrMDaNfQftfWzUyNJCvj8e3uC0iW36Fq2/3qkrDTwG8gBBZ09yLW7RYaQkRwHLvUgvIXWxuI2y3OX8CRxJS0ZZ/Hko+olYlJESGXkFlE+EmyTIdnPVFFVZ4E6FXtN4mnjZcnUs6PEEZhpTzakZWbwcFRJE9fhtXPGJPGqxn5tq4lcPYkNrcY35qTrG2exKtQpCEtOctApFNpGK4M/+0GQmNprGZ97JjXYAATC3AELiqIIIpXqi3vBUFf9AnPdK7ArVpzPnp8I79btgWBsegH4EgjAaYxOFCc0h3R+v4sARPhKBo6tqOGIA8B7dFXh7g3oxWWqS7SMucT3MqHe9OMhs7/cn/Zk6DGtaoSKKTNmgCN0iVGbehy8sxQDaBOayBnV1XxXeO2Th7Fj3k4cOGdFyOWycfWqiRuZIWElFzwPlh572PGjWnzvx/c9naN7p37kh/yhGM1gRDsdNIKenVrwADi1RN01TvFnszxqGPaoyL3xSEFngFL6lS7X5eyN4m7xtPmj6RGH1FSxQcPMexLLCWA1vFA4uFxhSxJHNun7EHo/0Qa5SDFcggTzvtGr39lFPvlNAYOoVesrQAZHJh2R+yz93g2dBLKqECO3K13JAPnsCyjndYHJvHWg+m90X3vt+cU3ukmBFr8KB8vGi2Z3jRcYCOPHhXL5fn+5hY9LI00OZlTTvMJr6avgd0CTastB9bH5I+YebGdLTq3QS1y1pdnyzPug54pTfCRco55IIySe/IxbihvlrdoRmd0FnT9RZ4XqZRhdSI0pkhu38Uoqz3Bmeg3LmuYBN0HbISVRGnsJsn0NkHRjwJmYD9csoSzVVbRuiJhdQHQdXghV5JgzivSL+cWuJp++J0QDaJnCB2eJX7+6Md6GlNVZxo46HZOoHDAIZ4EvjG3xRe0+q91z0oFUTF4KCglNA+nKm1Vt9rMHwbL9ASEaoaoQc7pjsGpBdxyCPVuEAoEpGP9A9L8WhqTRNUcOekkhKUSOLJhmljvYU+hAPcSzMs8Z3w1BlKZXZ3FyBqDb22eI8cK9kndIg1Vx08Z+mR6TxCjio8M6R2qh39CjEhSk1RcbeScvyRRACLhtETTYH8v2RVra7kNiPci4lGKshAywJwMWIzUDV7oYw2Epfit0C71gCovbHBr5yIPlshRi3u0ZPArk75DCtebwM3s52lOJVrhhlMZtk1N9GDp3eiOyzLzpRuXxTiv9DjGUxlMizBcUfVEVOA1HzR4g/paPNIwhOLqH5Ye/td6YPpmDJGkfem6cF50YgddhiK6TUqtp1vmLz0wLm3yOCgiIn4PdcaRjcZCjNnOPySXU+je6aOZnynRgulTEUPEz0vjFsMCMGCSqGSIb3DQEJFTEWBBTLK+9AJAuHbJWObgIHn9cDBNogizBFBgkqhkiG9w0BCRQxOB42AFMAdQByAGcAZQAgAEcAZQBuAGUAcgBhAHQAZQBkACAAQwBBACAANwBFAEMARAA3ADYAQQA3MC0wITAJBgUrDgMCGgUABBSBbYBjOEQfNvQ2QKk2OuN4UwHX0wQIcAUGGshaRlY=

[Script]

# fxiaoke-message = type=http-response,pattern=^https?:\/\/webpush\.fxiaoke\.com\/H\/V5Messenger\/checkUpdatedAsyncV2,requires-body=1,max-size=0,debug=1,script-path=fxiaoke_message.js

# 附带重载配置，Surge Pro 标题，可显示启动时间，点击刷新为重载配置
SurgePro_ReloadProfile = type=generic,timeout=10,script-path=https://raw.githubusercontent.com/fishingworld/something/main/PanelScripts/surgepro_reloadprofile.js,argument=icon=crown.fill&color=#f6c970
net-info-panel = script-path=https://raw.githubusercontent.com/Peng-YM/QuanX/master/Tools/Panels/NetInfo/net-info-panel.js,type=generic
IP-Check = type=generic,timeout=3,script-path=https://raw.githubusercontent.com/Rabbit-Spec/Surge/Master/Module/Panel/IP-Check/Moore/IP-Check.js
CFGPT = type=generic,timeout=6,script-path=https://raw.githubusercontent.com/getsomecat/GetSomeCats/Surge/modules/Panel/CFGPT/CFGPT_2.js,argument=icon=lasso.and.sparkles&iconerr=xmark.seal.fill&icon-color=#336FA9&iconerr-color=#D65C51
# 流量统计 点击以切换网络界面
TrafficStatistics = type=generic,timeout=10,script-path=https://raw.githubusercontent.com/fishingworld/something/main/PanelScripts/trafficstatistics.js,argument=icon=arrow.up.arrow.down.circle&color=#5d84f8
stream-all = type=generic,timeout=15,script-path=https://raw.githubusercontent.com/Rabbit-Spec/Surge/Master/Module/Panel/Stream-All/Moore/Stream-All.js
# 机场信息
# 必须修改的字段：你 encode 后的机场订阅链接
# piaoya = type=generic,timeout=10,script-path=https://raw.githubusercontent.com/mieqq/mieqq/master/sub_info_panel.js,script-update-interval=0,argument=url=https%3A%2F%2Fxn--04s008m.com%2Fapi%2Fv1%2Fclient%2Fsubscribe%3Ftoken%3D8a0c30c9b8aa71aec21baadc99abeb45&reset_day=29&title=PY&icon=opticaldisc&color=#5AC8FA&method=get
# dddd = type=generic,timeout=10,script-path=https://raw.githubusercontent.com/mieqq/mieqq/master/sub_info_panel.js,script-update-interval=0,argument=url=https%3A%2F%2Fdddd.lol%2Fapi%2Fv1%2Fclient%2Fsubscribe%3Ftoken%***********************************&title=DD&expire=false&icon=opticaldisc&color=#5AC8FA
# v2aky = type=generic,timeout=10,script-path=https://raw.githubusercontent.com/mieqq/mieqq/master/sub_info_panel.js,script-update-interval=0,argument=url=https%3A%2F%2Frenew.akicloud.tokyo%2Fapi%2Fv1%2Fclient%2Fsubscribe%3Ftoken%3D577656498e894a8420754e8c75f2c971&reset_day=9&title=V2&icon=opticaldisc&color=#5AC8FA&method=get
# efcloud = type=generic,timeout=10,script-path=https://raw.githubusercontent.com/mieqq/mieqq/master/sub_info_panel.js,script-update-interval=0,argument=url=https%3A%2F%2Fn15uvht4r659107p.eastasia.cloudapp.azure.com%2Fqueue%2Flist%3Ftoken%3D6886999197f4f8d6121343221e220692&reset_day=03&title=EF&icon=opticaldisc&color=#5AC8FA&method=get
# lightsr = type=generic,timeout=10,script-path=https://raw.githubusercontent.com/mieqq/mieqq/master/sub_info_panel.js,script-update-interval=0,argument=url=https%3A%2F%2F7.ttnamvka.com%2Flink%2FdWDoplqLkSBAJpqM%3Fclash%3D1&reset_day=21&title=lightsr&icon=opticaldisc&color=#5AC8FA&method=get
# disney+ 策略组控制
# 应当修改的字段 disneyGroup Disney+ 的策略组名称
# 详情请阅读：https://github.com/fishingworld/something/tree/main/DisneySelect
# DisneySelecter = type=generic,script-path=https://raw.githubusercontent.com/fishingworld/something/main/DisneySelect/disney_selecter.js,argument=icon1=checkmark.circle&color1=#55ba94&icon2=cursorarrow.click.badge.clock&color2=#ed6c84&icon3=xmark.shield&color3=#AF52DE&disneyGroup=🎥 迪士尼+
# DisneyChecker = type=cron,cronexp=35 4 * * *,wake-system=1,timeout=3600,script-path=https://raw.githubusercontent.com/fishingworld/something/main/DisneySelect/disney_checker.js,script-update-interval=86400,control-api=1
# netflix 策略组控制
# 必须更改的字段 netflixGroup 填写你 Netflix 策略组名称
# 详情请阅读：https://github.com/fishingworld/something/blob/main/NetflixSelect/README.md
# NetflixSelecter = type=generic,script-path=https://raw.githubusercontent.com/fishingworld/something/main/NetflixSelect/nf_autoselect.js,argument=icon1=checkmark.circle&color1=#55ba94&icon2=checkmark.circle.trianglebadge.exclamationmark&color2=#9a9ced&icon3=hand.raised.circle&color3=#ea5532&netflixGroup=🎥 奈飞视频
# NetflixChecker = type=cron,cronexp=5 4 * * *,wake-system=1,timeout=3600,script-path=https://raw.githubusercontent.com/fishingworld/something/main/NetflixSelect/nf_autocheck.js,script-update-interval=0,control-api=1
# 策略组面板 可重复配置 注意修改相应字段
# 必须更改的字段：group 填写需要显示的策略组名称

groupPanel = type=generic,timeout=10,script-path=https://raw.githubusercontent.com/fishingworld/something/main/groupPanel.js,argument=icon=network&color=#86abee&group=🧑‍💻 🇺🇸 US Smart
🐟 漏网之鱼 = type=generic,timeout=10,script-path=https://raw.githubusercontent.com/fishingworld/something/main/groupPanel.js,argument=icon=network&color=#86abee&group=🐟 漏网之鱼
💬 OpenAi = type=generic,timeout=10,script-path=https://raw.githubusercontent.com/fishingworld/something/main/groupPanel.js,argument=icon=network&color=#86abee&group=💬 OpenAi


# raycast-activate-backend.raycast.com = type=http-response,pattern=^https://backend.raycast.com/api/v1/me,requires-body=1,max-size=0,debug=1,script-path=activator.js,script-update-internal=86400
# raycast-translations-backend.raycast.com = type=http-request,pattern=^https://backend.raycast.com/api/v1/translations,requires-body=1,max-size=0,debug=1,script-path=activator.js,script-update-internal=86400
# raycast-me/trial_status-backend.raycast.com = type=http-request,pattern=^https://backend.raycast.com/api/v1/me/trial_status,requires-body=1,max-size=0,debug=1,script-path=activator.js,script-update-internal=86400
# raycast-me/sync-backend.raycast.com = type=http-request,pattern=^https://backend.raycast.com/api/v1/me/sync,requires-body=1,max-size=0,debug=1,script-path=activator.js,script-update-internal=86400
# raycast-ai/models-backend.raycast.com = type=http-request,pattern=^https://backend.raycast.com/api/v1/ai/models,requires-body=1,max-size=0,debug=1,script-path=activator.js,script-update-internal=86400
# raycast-ai/chat_completions-backend.raycast.com = type=http-request,pattern=^https://backend.raycast.com/api/v1/ai/chat_completions,requires-body=1,max-size=0,debug=1,script-path=activator.js,script-update-internal=86400
# paddle-activate-v3.paddleapi.com = type=http-request,pattern=^https://v3.paddleapi.com/3.2/license/activate,requires-body=1,max-size=0,debug=1,script-path=https://github.com/wibus-wee/activation-script/raw/gh-pages/activator.js,script-update-internal=86400
# paddle-validate-v3.paddleapi.com = type=http-request,pattern=^https://v3.paddleapi.com/3.2/license/verify,requires-body=1,max-size=0,debug=1,script-path=https://github.com/wibus-wee/activation-script/raw/gh-pages/activator.js,script-update-internal=86400
# itunes-verifyReceipt-buy.itunes.apple.com = type=http-request,pattern=^https://buy.itunes.apple.com/verifyReceipt,requires-body=1,max-size=0,debug=1,script-path=https://github.com/wibus-wee/activation-script/raw/gh-pages/activator.js,script-update-internal=86400
# lemonSqueezy-activate-api.lemonsqueezy.com = type=http-request,pattern=^https://api.lemonsqueezy.com/v1/licenses/activate,requires-body=1,max-size=0,debug=1,script-path=https://github.com/wibus-wee/activation-script/raw/gh-pages/activator.js,script-update-internal=86400
# lemonSqueezy-validate-api.lemonsqueezy.com = type=http-request,pattern=^https://api.lemonsqueezy.com/v1/licenses/validate,requires-body=1,max-size=0,debug=1,script-path=https://github.com/wibus-wee/activation-script/raw/gh-pages/activator.js,script-update-internal=86400
# nf_checker = type=generic,timeout=30,debug=1,script-path=nf_autocheck.js

[Panel]
# Surge Pro 标题，可显示启动时间，点击刷新为重载配置
SurgePro_ReloadProfile = script-name=SurgePro_ReloadProfile,update-interval=1
net-info-panel = title=网络状态,content=请刷新,style=info,script-name=net-info-panel
IP-Check = script-name=IP-Check,title=节点详情,content=请刷新,style=info,update-interval=1
# GPT
CFGPT = script-name=CFGPT,update-interval=-1
stream-all = script-name=stream-all,title=流媒体解锁检测,content=请刷新面板,update-interval=3600
# 流量统计
TrafficStatistics = script-name=TrafficStatistics,update-interval=1
# 策略组面板 可重复配置 注意修改相应字段
groupPanel = script-name=groupPanel,update-interval=5
groupPanel = script-name=🐟 漏网之鱼,update-interval=5
groupPanel = script-name=💬 OpenAi,update-interval=5
# 机场信息
efcloud = script-name=efcloud,update-interval=43200

[WireGuard Home]
private-key = yIyav/JHZHLyjceLZDI9p/SaP0CZKwCptvDKynxDs2A=
self-ip = *************
dns-server = *******
mtu = 1420
peer = (public-key = xD9fjrI57Vi/APfXogDzfvzTSe3GYmJL016W1h34cBY=, allowed-ips = *************/24, endpoint = ************:51821)
