{"policyGroupSettings": {"🧑‍💻 Asian Smart": {"cardBackgroundIdx": 10, "coloredIcon": "B1::Plants::Pear", "iconManuallySet": false}, "🧑‍💻 US Smart": {"cardBackgroundIdx": 11, "coloredIcon": "B1::Plants::Peach", "iconManuallySet": false}, "🇭🇰 香港节点": {"cardBackgroundIdx": 30, "coloredIcon": "B1::Plants::Cherry", "iconManuallySet": false}, "📺 Infuse": {"cardBackgroundIdx": 18, "coloredIcon": "B1::Plants::Grapes", "iconManuallySet": false}, "💰 Asian Smart": {"cardBackgroundIdx": 22, "coloredIcon": "B1::Plants::Carrot", "iconManuallySet": true}, "🎥 奈飞视频": {"cardBackgroundIdx": 28, "coloredIcon": "B1::Plants::Blueberry", "iconManuallySet": true}, "🧑‍💻 US LB": {"cardBackgroundIdx": 1, "coloredIcon": "B1::Plants::Mango", "iconManuallySet": false}, "piavpn": {"cardBackgroundIdx": 26, "coloredIcon": "B1::Plants::Mango", "iconManuallySet": false}, "📢 谷歌FCM": {"cardBackgroundIdx": 4, "coloredIcon": "B1::Plants::Blueberry", "iconManuallySet": false}, "🚀 手动切换": {"cardBackgroundIdx": 3, "coloredIcon": "B1::Plants::Grapes", "iconManuallySet": false}, "fs_jp自动": {"cardBackgroundIdx": 5, "coloredIcon": "B1::Plants::Carrot", "iconManuallySet": false}, "🍎 苹果服务": {"cardBackgroundIdx": 12, "coloredIcon": "B1::Plants::Carrot", "iconManuallySet": false}, "🛑 广告拦截": {"cardBackgroundIdx": 18, "coloredIcon": "B1::Plants::Ra<PERSON>berry", "iconManuallySet": false}, "piavpn smart": {"cardBackgroundIdx": 13, "coloredIcon": "B1::Plants::Banana", "iconManuallySet": false}, "🍃 应用净化": {"cardBackgroundIdx": 24, "coloredIcon": "B1::Plants::Banana", "iconManuallySet": false}, "🎯 全球直连": {"cardBackgroundIdx": 24, "coloredIcon": "B1::Plants::Pineapple", "iconManuallySet": false}, "🎮 游戏平台": {"cardBackgroundIdx": 10, "coloredIcon": "B1::Plants::Peach", "iconManuallySet": false}, "Ⓜ️ 微软云盘": {"cardBackgroundIdx": 14, "coloredIcon": "B1::Plants::Orange", "iconManuallySet": false}, "🇺🇸 免费节点": {"cardBackgroundIdx": 24, "coloredIcon": "B1::Plants::Banana", "iconManuallySet": true}, "📺 哔哩哔哩": {"cardBackgroundIdx": 23, "coloredIcon": "B1::Plants::Banana", "iconManuallySet": false}, "🇺🇸 免费自动": {"cardBackgroundIdx": 28, "coloredIcon": "B1::Plants::Grapes", "iconManuallySet": false}, "✈️ 20150122": {"cardBackgroundIdx": 24, "coloredIcon": "B1::Plants::Ra<PERSON>berry", "iconManuallySet": false}, "📹 油管视频": {"cardBackgroundIdx": 27, "coloredIcon": "B1::Plants::Ra<PERSON>berry", "iconManuallySet": false}, "🎥 迪士尼+": {"cardBackgroundIdx": 31, "coloredIcon": "B1::Plants::Pineapple", "iconManuallySet": true}, "Ⓜ️ 微软服务": {"cardBackgroundIdx": 28, "coloredIcon": "B1::Plants::Pineapple", "iconManuallySet": false}, "🎶 网易音乐": {"cardBackgroundIdx": 8, "coloredIcon": "B1::Plants::Banana", "iconManuallySet": false}, "🌍 国外媒体": {"cardBackgroundIdx": 17, "coloredIcon": "B1::Plants::Cherry", "iconManuallySet": false}, "✈️ 52pokemon.cc 1109": {"cardBackgroundIdx": 32, "coloredIcon": "B1::Plants::Cutted Watermelon", "iconManuallySet": false}, "✈️ 机场xqc.com": {"cardBackgroundIdx": 5, "coloredIcon": "B1::Plants::Orange", "iconManuallySet": false}, "🌏 国内媒体": {"cardBackgroundIdx": 24, "coloredIcon": "B1::Plants::Pear", "iconManuallySet": false}, "🎥 奈飞节点": {"cardBackgroundIdx": 13, "coloredIcon": "B1::Plants::Carrot", "iconManuallySet": false}, "<internal-placeholder-global>": {"cardBackgroundIdx": 12, "coloredIcon": "B1::Plants::Blueberry", "iconManuallySet": false}, "Speedtest": {"cardBackgroundIdx": 8, "coloredIcon": "B1::Plants::Blueberry", "iconManuallySet": false}, "💰 US Smart": {"cardBackgroundIdx": 7, "coloredIcon": "B1::Plants::Banana", "iconManuallySet": false}, "✈️ 机场节点 Smart": {"cardBackgroundIdx": 6, "coloredIcon": "B1::Plants::Cherry", "iconManuallySet": false}, "🐝 FS_JP": {"cardBackgroundIdx": 16, "coloredIcon": "B1::Plants::Carrot", "iconManuallySet": false}, "🐟 漏网之鱼": {"cardBackgroundIdx": 6, "coloredIcon": "B1::Plants::Cherry", "iconManuallySet": false}, "🇺🇲 美国节点": {"cardBackgroundIdx": 3, "coloredIcon": "B1::Plants::Carrot", "iconManuallySet": false}, "🚀 节点选择": {"cardBackgroundIdx": 2, "coloredIcon": "B1::Plants::<PERSON><PERSON>", "iconManuallySet": false}, "📲 电报消息": {"cardBackgroundIdx": 33, "coloredIcon": "B1::Plants::Pear", "iconManuallySet": false}, "✈️ 一": {"cardBackgroundIdx": 25, "coloredIcon": "B1::Plants::Ra<PERSON>berry", "iconManuallySet": false}, "🇯🇵 日本节点": {"cardBackgroundIdx": 4, "coloredIcon": "B1::Plants::Cherry", "iconManuallySet": false}, "🇰🇷 韩国节点": {"cardBackgroundIdx": 29, "coloredIcon": "B1::Plants::Orange", "iconManuallySet": true}, "✈️ 超级机场": {"cardBackgroundIdx": 15, "coloredIcon": "B1::Plants::Pear", "iconManuallySet": false}, "🇸🇬 狮城节点": {"cardBackgroundIdx": 27, "coloredIcon": "B1::Plants::Carrot", "iconManuallySet": true}, "🛍️ Amazon": {"cardBackgroundIdx": 30, "coloredIcon": "B1::Plants::Pineapple", "iconManuallySet": false}, "✈️ 机场节点": {"cardBackgroundIdx": 29, "coloredIcon": "B1::Plants::Pineapple", "iconManuallySet": false}, "💬 OpenAi": {"cardBackgroundIdx": 14, "coloredIcon": "B1::Plants::Pineapple", "iconManuallySet": false}, "🇺🇸美国": {"cardBackgroundIdx": 7, "coloredIcon": "B1::Plants::Blueberry", "iconManuallySet": false}, "📺 巴哈姆特": {"cardBackgroundIdx": 21, "coloredIcon": "B1::Plants::Banana", "iconManuallySet": false}, "🎥 PrimeVideo": {"cardBackgroundIdx": 20, "coloredIcon": "B1::Plants::Pineapple", "iconManuallySet": false}, "🇹🇼 台湾节点": {"cardBackgroundIdx": 6, "coloredIcon": "B1::Plants::Pineapple", "iconManuallySet": true}, "✈️ efccloud": {"cardBackgroundIdx": 3, "coloredIcon": "B1::Plants::Nut", "iconManuallySet": false}, "♻️ 自动选择": {"cardBackgroundIdx": 4, "coloredIcon": "B1::Plants::Grapes", "iconManuallySet": false}, "🧑‍💻 US LB Persistent": {"cardBackgroundIdx": 9, "coloredIcon": "B1::Plants::Nut", "iconManuallySet": false}}, "version": 1}