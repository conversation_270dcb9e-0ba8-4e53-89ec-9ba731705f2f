#!MANAGED-CONFIG https://sub.xeton.dev/sub?config=https%3A%2F%2Fraw.githubusercontent.com%2FACL4SSR%2FACL4SSR%2Fmaster%2FClash%2Fconfig%2FACL4SSR_Online.ini&insert=false&target=surge&url=ss%3A%2F%2FYWVzLTI1Ni1jZmI6YW1hem9uc2tyMDU%40************%3A443%23aws%25E9%259F%25A9%25E5%259B%25BD%7Css%3A%2F%2FYWVzLTI1Ni1jZmI6YW1hem9uc2tyMDU%40**************%3A443%23aws%25E6%2596%25B0%25E5%258A%25A0%25E5%259D%25A11%7Css%3A%2F%2FYWVzLTI1Ni1jZmI6YW1hem9uc2tyMDU%40*************%3A443%23aws%25E6%2596%25B0%25E5%258A%25A0%25E5%259D%25A12%7Css%3A%2F%2FY2hhY2hhMjAtaWV0Zi1wb2x5MTMwNTpkY2JjMWM3Zi1jMzIzLTQzNjUtYmQ0OC1jZjc2N2Y5MDgxY2U%40nc.cm.go002.xyz%3A19521%23aka%2520%25E5%258F%25B0%25E6%25B9%25BE%2520%25E5%258F%25B0%25E5%258C%2597%25E5%25B8%2582%2520%25E5%258E%259F%25E7%2594%259F%7Css%3A%2F%2FY2hhY2hhMjAtaWV0Zi1wb2x5MTMwNTpkY2JjMWM3Zi1jMzIzLTQzNjUtYmQ0OC1jZjc2N2Y5MDgxY2U%40nc.ct1.go002.xyz%3A19511%23%25F0%259F%2587%25AD%25F0%259F%2587%25B0%2520%25E5%25AE%25B6%25E5%25AE%25BD%2520%25E5%258E%259F%25E7%2594%259F&ver=4 interval=86400 strict=false

[General]
loglevel = notify
bypass-system = true
skip-proxy = 127.0.0.1,***********/16,10.0.0.0/8,**********/12,**********/10,localhost,*.local,e.crashlytics.com,captive.apple.com,::ffff:0:0:0:0/1,::ffff:128:0:0:0/1
#DNS设置或根据自己网络情况进行相应设置
bypass-tun = ***********/16,10.0.0.0/8,**********/12
dns-server = ************,*********

[Script]
http-request https?:\/\/.*\.iqiyi\.com\/.*authcookie= script-path=https://raw.githubusercontent.com/NobyDa/Script/master/iQIYI-DailyBonus/iQIYI.js

[Proxy]
DIRECT = direct
aws韩国 = ss, ************, 443, encrypt-method=aes-256-cfb, password=amazonskr05
aws新加坡1 = ss, **************, 443, encrypt-method=aes-256-cfb, password=amazonskr05
aws新加坡2 = ss, *************, 443, encrypt-method=aes-256-cfb, password=amazonskr05
aka 台湾 台北市 原生 = ss, nc.cm.go002.xyz, 19521, encrypt-method=chacha20-ietf-poly1305, password=dcbc1c7f-c323-4365-bd48-cf767f9081ce
家宽 原生 = ss, nc.ct1.go002.xyz, 19511, encrypt-method=chacha20-ietf-poly1305, password=dcbc1c7f-c323-4365-bd48-cf767f9081ce

[Proxy Group]
🚀 节点选择 = select,♻️ 自动选择,DIRECT,aws韩国,aws新加坡1,aws新加坡2,aka 台湾 台北市 原生,家宽 原生
♻️ 自动选择 = url-test,aws韩国,aws新加坡1,aws新加坡2,aka 台湾 台北市 原生,家宽 原生,url=http://www.gstatic.com/generate_204,interval=300,tolerance=50
🌍 国外媒体 = select,🚀 节点选择,♻️ 自动选择,🎯 全球直连,aws韩国,aws新加坡1,aws新加坡2,aka 台湾 台北市 原生,家宽 原生
📲 电报信息 = select,🚀 节点选择,🎯 全球直连,aws韩国,aws新加坡1,aws新加坡2,aka 台湾 台北市 原生,家宽 原生
Ⓜ️ 微软服务 = select,🎯 全球直连,🚀 节点选择,aws韩国,aws新加坡1,aws新加坡2,aka 台湾 台北市 原生,家宽 原生
🍎 苹果服务 = select,🚀 节点选择,🎯 全球直连,aws韩国,aws新加坡1,aws新加坡2,aka 台湾 台北市 原生,家宽 原生
📢 谷歌FCM = select,🚀 节点选择,🎯 全球直连,♻️ 自动选择,aws韩国,aws新加坡1,aws新加坡2,aka 台湾 台北市 原生,家宽 原生
🎯 全球直连 = select,DIRECT,🚀 节点选择,♻️ 自动选择
🛑 全球拦截 = select,REJECT,DIRECT
🍃 应用净化 = select,REJECT,DIRECT
🐟 漏网之鱼 = select,🚀 节点选择,🎯 全球直连,♻️ 自动选择,aws韩国,aws新加坡1,aws新加坡2,aka 台湾 台北市 原生,家宽 原生

[Rule]
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/LocalAreaNetwork.list,🎯 全球直连,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/UnBan.list,🎯 全球直连,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanAD.list,🛑 全球拦截,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanProgramAD.list,🍃 应用净化,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/GoogleFCM.list,📢 谷歌FCM,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/GoogleCN.list,🎯 全球直连,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/SteamCN.list,🎯 全球直连,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Microsoft.list,Ⓜ️ 微软服务,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Apple.list,🍎 苹果服务,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Telegram.list,📲 电报信息,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ProxyMedia.list,🌍 国外媒体,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ProxyLite.list,🚀 节点选择,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ChinaDomain.list,🎯 全球直连,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ChinaCompanyIp.list,🎯 全球直连,update-interval=86400
GEOIP,CN,🎯 全球直连
FINAL,🐟 漏网之鱼

