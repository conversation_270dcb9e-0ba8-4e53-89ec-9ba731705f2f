[General]
# 日志等级: warning, notify, info, verbose (默认值: notify)
loglevel = warning
# 跳过某个域名或者 IP 段，这些目标主机将不会由 Surge Proxy 处理。(在 macOS
# 版本中，如果启用了 Set as System Proxy,  那么这些值会被写入到系统网络代理
# 设置中.)
skip-proxy = 127.0.0.1, ***********/16, 10.0.0.0/8, **********/12, **********/10, **********/10, ***********,localhost, *.local, *.firstshare.cn, *.foneshare.cn
# 强制使用特定的 DNS 服务器
dns-server = *********, ***************, system
# 以下参数仅供 iOS 版本使用
# 将系统相关请求交给 Surge TUN 处理，并自动追加规则
# "IP-CIDR,********/8,DIRECT,no-resolve"
# bypass-system = true
# 将特定 IP 段跳过 Surge TUN，详见 Manual
bypass-tun = ***********/16, 10.0.0.0/8, **********/12
# 是否截取并保存 HTTP 流量 (启用后将对性能有较大影响) (默认值: false)
replica = false
# 是否启动完整的 IPv6 支持 (默认值: false)
ipv6 = false
# 以下参数仅供 macOS 版本使用（多端口监听仅 Surge 3 支持）
internet-test-url = http://www.apple.com/library/test/success.html
proxy-test-url = http://www.apple.com/library/test/success.html
# 测速地址
; internet-test-url = http://cp.cloudflare.com/generate_204
; proxy-test-url = http://cp.cloudflare.com/generate_204
show-primary-interface-changed-notification = true
proxy-settings-interface = Primary Interface (Auto)
# 其它
# external-controller-access = password@0.0.0.0:6170
menu-bar-show-speed = false
allow-wifi-access = true
hide-crashlytics-request = true
http-api = gchr_surge@0.0.0.0:6171
http-api-tls = true
always-real-ip = *.srv.nintendo.net, *.stun.playstation.net, xbox.*.microsoft.com, *.xboxlive.com, *.fspage.com
geoip-maxmind-url = https://github.com/Hackl0us/GeoIP2-CN/raw/release/Country.mmdb
test-timeout = 20
http-api-web-dashboard = true
show-error-page-for-reject = true
read-etc-hosts = true
exclude-simple-hostnames = true
http-listen = 0.0.0.0
socks5-listen = 0.0.0.0

[Proxy]
SPK-USAA = vmess, ************, 18595, username=02282bfb-5761-4ebc-b8c0-180c3c79ea25, vmess-aead=true
🇹🇼 GCP = vmess, gcp.gchr.live, 443, username=be30fe72-2e18-46b8-bcd8-969e7ae0db7e, ws=true, ws-path=/be30fe72-2e18-46b8-bcd8-969e7ae0db7e, ws-headers=Host:"gcp.gchr.live", vmess-aead=true, tls=true, sni=gcp.gchr.live
serv00 = vmess, *************, 443, username=69f2eaaf-3838-46b2-98ca-d29a7ae310fe, sni=serv00.068666.xyz, ws=true, ws-path=/gchr007-vmess?ed=2048, ws-headers=Host:"serv00.068666.xyz", vmess-aead=true, tls=true
cf-trojan = trojan, trojan.068666.xyz, 443, password=gchr2024, sni=trojan.068666.xyz, ws=true, ws-headers=Host:"trojan.068666.xyz", test-url=http://www.google.com/generate_204
🇭🇰 azure_hk = vmess, **************, 55198, username=40ad7ae7-1bc7-470b-b51b-a512dd867c6c, vmess-aead=true, tfo=true
🇭🇰 azure_hk_hy2 = hysteria2, **************, 61082, password=4945fba1, skip-cert-verify=true, sni=www.bing.com, download-bandwidth=1000
🇯🇵 fs_jp = vmess, fsjp.068666.xyz, 443, username=1a871420-f0fc-4a2c-adca-1badbfae276c, ws=true, ws-path=/, ws-headers=Host:"fsjp.068666.xyz", vmess-aead=true, tls=true, sni=fsjp.068666.xyz
🇺🇸 zgo = vmess, zgo.068666.xyz, 25477, username=ff4461e0-669d-4c35-8bd1-c41a1134c591, ws=true, ws-path=/ray, vmess-aead=true, tls=true, tfo=true, skip-cert-verify=true, sni=zgo.068666.xyz
🇺🇸 zgo home cdn = vmess, **************, 443, username=2367db51-efaf-42cc-bb18-cdf3582c9bde, ws=true, ws-path=/ray, ws-headers=Host:"zgo.068666.xyz", vmess-aead=true, tls=true, tfo=true, skip-cert-verify=true, sni=zgo.068666.xyz
🇺🇸 zgo_hy2 = hysteria2, *************, 19893, password=b965256f, skip-cert-verify=true, sni=www.bing.com, download-bandwidth=1000
🇺🇸 RackNerd_DC02_hy2 = hysteria2, *************, 44966, password=6bdb2f25, skip-cert-verify=true, sni=www.bing.com, download-bandwidth=1000
🇺🇸 RackNerd_hy2 = hysteria2, *************, 60557, password=3b5efc79, skip-cert-verify=true, sni=www.bing.com, download-bandwidth=1000
🇺🇸 RackNerd_SanJose_hy2 = hysteria2, ***************, 58424, password=8deab58a, skip-cert-verify=true, sni=www.bing.com, download-bandwidth=250, test-url=http://www.google.com/generate_204
🇺🇸 RackNerd San Jose work cdn = vmess, **************, 443, username=3c5de089-0793-4367-92bd-6279889d3af1, skip-cert-verify=true, sni=rnsj.gchr.live, ws=true, ws-path=/ray, ws-headers=Host:"rnsj.gchr.live", vmess-aead=true, tls=true, tfo=true
🇺🇸 RackNerd San Jose 02 = vmess, *************, 443, username=3c5de089-0793-4367-92bd-6279889d3af1, skip-cert-verify=true, sni=rnsj.gchr.live, ws=true, ws-path=/ray, ws-headers=Host:"rnsj.gchr.live", vmess-aead=true, tls=true, tfo=true, test-url=http://www.gstatic.com/generate_204
🇺🇸 RackNerd San Jose work01 = vmess, *************, 443, username=3c5de089-0793-4367-92bd-6279889d3af1, skip-cert-verify=true, sni=rnsj.gchr.live, ws=true, ws-path=/ray, ws-headers=Host:"rnsj.gchr.live", vmess-aead=true, tls=true, tfo=true, test-url=http://www.gstatic.com/generate_204
🇺🇸 RackNerd San Jose cdn H = vmess, *************, 443, username=3c5de089-0793-4367-92bd-6279889d3af1, skip-cert-verify=true, sni=rnsj.gchr.live, ws=true, ws-path=/ray, ws-headers=Host:"rnsj.gchr.live", vmess-aead=true, tls=true, tfo=true
🇺🇸 RackNerd San Jose home cdn 副本 = vmess, **************, 443, username=3c5de089-0793-4367-92bd-6279889d3af1, skip-cert-verify=true, sni=rnsj.gchr.live, ws=true, ws-path=/ray, ws-headers=Host:"rnsj.gchr.live", vmess-aead=true, tls=true, tfo=true
🇺🇸 RackNerd Chicago cdn work = vmess, *************, 443, username=55af6fd4-4ce2-40ad-bb0e-bcc51538d781, skip-cert-verify=true, sni=rnc.gchr.live, ws=true, ws-path=/ray, ws-headers=Host:"rnc.gchr.live", vmess-aead=true, tls=true, tfo=true
🇺🇸 RackNerd Chicago cdn = vmess, ***************, 443, username=55af6fd4-4ce2-40ad-bb0e-bcc51538d781, skip-cert-verify=true, sni=rnc.gchr.live, ws=true, ws-path=/ray, ws-headers=Host:"rnc.gchr.live", vmess-aead=true, tls=true, tfo=true
🇺🇸 RackNerd DC02 副本 = vmess, **************, 443, username=c5508ca3-175a-4c98-bb25-2d7d03cb1110, skip-cert-verify=true, sni=dc2.gchr.tech, ws=true, ws-path=/ray, ws-headers=Host:"dc2.gchr.tech", vmess-aead=true, tls=true, tfo=true
🇺🇸 RackNerd DC02 cdn2 = vmess, **************, 443, username=c5508ca3-175a-4c98-bb25-2d7d03cb1110, skip-cert-verify=true, sni=dc2.gchr.tech, ws=true, ws-path=/ray, ws-headers=Host:"dc2.gchr.tech", vmess-aead=true, tls=true, tfo=true
🇺🇸 RackNerd DC02 cdn3 = vmess, *************, 443, username=c5508ca3-175a-4c98-bb25-2d7d03cb1110, skip-cert-verify=true, sni=dc2.gchr.tech, ws=true, ws-path=/ray, ws-headers=Host:"dc2.gchr.tech", vmess-aead=true, tls=true, tfo=true
🇺🇸 RackNerd DC02 cdn3 副本 = vmess, *************, 443, username=c5508ca3-175a-4c98-bb25-2d7d03cb1110, skip-cert-verify=true, sni=dc2.gchr.tech, ws=true, ws-path=/ray, ws-headers=Host:"dc2.gchr.tech", vmess-aead=true, tls=true, tfo=true
🇺🇸 atlantic_hy2 = hysteria2, ************, 47040, password=6db3d628, skip-cert-verify=true, sni=www.bing.com, download-bandwidth=1000
🇺🇸 dmit = trojan, dmit.068666.xyz, 51498, username=cyemt6vj, password=lm3EokjcKx, sni=dmit.068666.xyz
🇸🇬 do3_hy2 = hysteria2, *************, 7684, password=68f9b5d8, skip-cert-verify=true, sni=www.bing.com, download-bandwidth=1000
🇸🇬 do3-sg-vmess = vmess, *************, 443, username=9252e0d7-1552-4530-ad00-6258f5a49cc2, skip-cert-verify=true, sni=do3sg.068666.xyz, ws=true, ws-path=/ray, ws-headers=Host:"do3sg.068666.xyz", vmess-aead=true, tls=true, tfo=true, test-url=http://www.gstatic.com/generate_204
🇸🇬 do3-sg-cdn-work = vmess, *************, 443, username=9252e0d7-1552-4530-ad00-6258f5a49cc2, skip-cert-verify=true, sni=do3sg.068666.xyz, ws=true, ws-path=/ray, ws-headers=Host:"do3sg.068666.xyz", vmess-aead=true, tls=true, tfo=true, test-url=http://www.gstatic.com/generate_204
🇸🇬 do3sg-snell = snell, *************, 2345, psk=bIUuBexbsVXtlsg4, obfs=http, obfs-host=www.bing.com, version=4, tfo=true
🇸🇬 do3-sg-cdn-home = vmess, *************, 443, username=9252e0d7-1552-4530-ad00-6258f5a49cc2, skip-cert-verify=true, sni=do3sg.068666.xyz, ws=true, ws-path=/ray, ws-headers=Host:"do3sg.068666.xyz", vmess-aead=true, tls=true, tfo=true, test-url=http://www.gstatic.com/generate_204
🇸🇬 do3-sg-cdn-home-2 = vmess, **************, 443, username=9252e0d7-1552-4530-ad00-6258f5a49cc2, skip-cert-verify=true, sni=do3sg.068666.xyz, ws=true, ws-path=/ray, ws-headers=Host:"do3sg.068666.xyz", vmess-aead=true, tls=true, tfo=true, test-url=http://www.gstatic.com/generate_204
🇸🇬 do3-sg-cdn-work02 = vmess, **************, 443, username=9252e0d7-1552-4530-ad00-6258f5a49cc2, skip-cert-verify=true, sni=do3sg.068666.xyz, ws=true, ws-path=/ray, ws-headers=Host:"do3sg.068666.xyz", vmess-aead=true, tls=true, tfo=true, test-url=http://www.gstatic.com/generate_204
🇸🇬 fssg =vmess,fssg.068666.xyz,443,username=528e1788-0f24-4c6f-92a2-6f389e7bf87b,ws=true,ws-path=/,ws-headers=Host:"fssg.068666.xyz",vmess-aead=true,tls=true,sni=fssg.068666.xyz

[Proxy Group]
🚀 节点选择 = select,    🇺🇸 免费节点, 🇭🇰 香港节点, 🇹🇼 台湾节点, 🇺🇲 美国节点, 🇯🇵 日本节点, 🇸🇬 狮城节点, 🇰🇷 韩国节点, 🎥 奈飞节点, include-all-proxies=1
🚀 手动切换 = select, include-all-proxies=true, timeout=50
♻️ 自动选择 = url-test, include-all-proxies=true, timeout=50, url=http://www.gstatic.com/generate_204, interval=300, tolerance=50
📲 电报消息 = select, 🚀 节点选择,   ♻️ 自动选择, 🇸🇬 狮城节点, 🇭🇰 香港节点, 🇹🇼 台湾节点, 🇯🇵 日本节点, 🇺🇲 美国节点, 🇰🇷 韩国节点, 🚀 手动切换, DIRECT
💬 OpenAi = select, 🚀 节点选择, ♻️ 自动选择, 🇸🇬 狮城节点, 🇭🇰 香港节点, 🇹🇼 台湾节点, 🇯🇵 日本节点, 🇺🇲 美国节点, 🇰🇷 韩国节点, 🚀 手动切换, DIRECT
📹 油管视频 = select, cf-trojan, 🇺🇸 免费节点,    🚀 节点选择, ♻️ 自动选择, 🇸🇬 狮城节点, 🇭🇰 香港节点, 🇹🇼 台湾节点, 🇯🇵 日本节点, 🇺🇲 美国节点, 🇰🇷 韩国节点, 🚀 手动切换, DIRECT
🎥 奈飞视频 = select, 🎥 奈飞节点, 🇸🇬 狮城节点, 🇺🇲 美国节点, 🇭🇰 香港节点, 🚀 节点选择
🎥 迪士尼+ = select, include-all-proxies=true, timeout=20, policy-regex-filter=(🇺🇸)|(美)|(States)|(US)|(🇸🇬)|(新)|(Singapore)|(SG), url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
🎥 PrimeVideo = select, 🎥 奈飞节点, 🇸🇬 狮城节点, 🇺🇲 美国节点, 🇭🇰 香港节点, 🚀 节点选择
📺 巴哈姆特 = select, 🇹🇼 台湾节点, 🚀 节点选择, 🚀 手动切换, DIRECT
📺 哔哩哔哩 = select, 🎯 全球直连, 🇹🇼 台湾节点, 🇭🇰 香港节点
🌍 国外媒体 = select,   🚀 节点选择, ♻️ 自动选择, 🇭🇰 香港节点, 🇹🇼 台湾节点, 🇸🇬 狮城节点, 🇯🇵 日本节点, 🇺🇲 美国节点, 🇰🇷 韩国节点, 🚀 手动切换, DIRECT
🌏 国内媒体 = select, DIRECT, 🇭🇰 香港节点, 🇹🇼 台湾节点, 🇸🇬 狮城节点, 🇯🇵 日本节点, 🚀 手动切换
📢 谷歌FCM = select, DIRECT, 🚀 节点选择, 🇺🇲 美国节点, 🇭🇰 香港节点, 🇹🇼 台湾节点, 🇸🇬 狮城节点, 🇯🇵 日本节点, 🇰🇷 韩国节点, 🚀 手动切换
Ⓜ️ 微软云盘 = select, DIRECT, 🚀 节点选择, 🇺🇲 美国节点, 🇭🇰 香港节点, 🇹🇼 台湾节点, 🇸🇬 狮城节点, 🇯🇵 日本节点, 🇰🇷 韩国节点, 🚀 手动切换
Ⓜ️ 微软服务 = select, DIRECT, 🚀 节点选择, 🇺🇲 美国节点, 🇭🇰 香港节点, 🇹🇼 台湾节点, 🇸🇬 狮城节点, 🇯🇵 日本节点, 🇰🇷 韩国节点, 🚀 手动切换
🍎 苹果服务 = select, DIRECT, 🚀 节点选择, 🇺🇲 美国节点, 🇭🇰 香港节点, 🇹🇼 台湾节点, 🇸🇬 狮城节点, 🇯🇵 日本节点, 🇰🇷 韩国节点, 🚀 手动切换
🎮 游戏平台 = select, DIRECT, 🚀 节点选择, 🇺🇲 美国节点, 🇭🇰 香港节点, 🇹🇼 台湾节点, 🇸🇬 狮城节点, 🇯🇵 日本节点, 🇰🇷 韩国节点, 🚀 手动切换
🎶 网易音乐 = select, DIRECT, 🚀 节点选择, ♻️ 自动选择
🎯 全球直连 = select, DIRECT, 🚀 节点选择, ♻️ 自动选择
🛑 广告拦截 = select, REJECT, DIRECT
🍃 应用净化 = select, REJECT, DIRECT
🐟 漏网之鱼 = select,   🚀 节点选择, ♻️ 自动选择, DIRECT, 🇺🇸 免费节点, 🇭🇰 香港节点, 🇹🇼 台湾节点, 🇸🇬 狮城节点, 🇯🇵 日本节点, 🇺🇲 美国节点, 🇰🇷 韩国节点, 🚀 手动切换
# > 外部节点自动匹配
# > 匹配到关键字，自动收纳为节点组
🇭🇰 香港节点 = select, DIRECT, include-all-proxies=true, policy-regex-filter=(🇭🇰)|(港)|(Hong)|(HK)|(hk), url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
🇹🇼 台湾节点 = select, DIRECT, include-all-proxies=true, timeout=20, policy-regex-filter=(🇨🇳)|(🇹🇼)|(台)|(Tai)|(TW), url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
🇺🇲 美国节点 = select, DIRECT, include-all-proxies=true, timeout=20, policy-regex-filter=(🇺🇸)|(美)|(States)|(US), url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
🇯🇵 日本节点 = select, DIRECT, include-all-proxies=true, timeout=20, policy-regex-filter=(🇯🇵)|(日)|(Japan)|(JP), url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
🇸🇬 狮城节点 = select, DIRECT, include-all-proxies=true, timeout=20, policy-regex-filter=(🇸🇬)|(新)|(Singapore)|(SG), url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
🇰🇷 韩国节点 = select, DIRECT, include-all-proxies=true, timeout=20, policy-regex-filter=(🇰🇷)|(韩)|(Korea)|(KR)|(대한민국), url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
🎥 奈飞节点 = select, DIRECT, include-all-proxies=true, timeout=20, policy-regex-filter=(🇭🇰)|(港)|(Hong)|(HK)|(hk)|(NF)|(奈飞)|(Netflix)|(video)|(Video)|(nf), url=http://www.gstatic.com/generate_204, interval=300, tolerance=150
🇺🇸 免费节点 = select, DIRECT, include-all-proxies=true, timeout=20, policy-regex-filter=(cf), url=http://www.gstatic.com/generate_204, interval=300, tolerance=150

[Rule]
DOMAIN-SUFFIX,tinyurl.com,🚀 节点选择
DOMAIN-SUFFIX,enjoy.bot,🚀 节点选择
DOMAIN-SUFFIX,greencloudvps.com,DIRECT
DOMAIN-SUFFIX,068666.xyz,DIRECT
DOMAIN-SUFFIX,mixpanel.com,🚀 节点选择
PROCESS-NAME,/Applications/qbittorrent.app/Contents/MacOS/qbittorrent,🚀 节点选择
PROCESS-NAME,/Applications/MarginNote 4.app/Contents/MacOS/MarginNote 4,REJECT
PROCESS-NAME,/Applications/Screen Studio.app/Contents/MacOS/Screen Studio,REJECT
DOMAIN,order.luckydogsoft.com,REJECT
DOMAIN-SUFFIX,you.com,🇺🇲 美国节点
DOMAIN,rss.gchr.tech,DIRECT
DOMAIN-SUFFIX,mypikpak.com,🚀 节点选择
DOMAIN,www.xmind.app,REJECT
DOMAIN,platform.deepseek.com,🚀 节点选择
IP-CIDR,**************/32,DIRECT,no-resolve
IP-CIDR,*************/32,DIRECT,no-resolve
IP-CIDR,************/32,🚀 节点选择,no-resolve
IP-CIDR,**************/32,DIRECT,no-resolve
DOMAIN,sstats.adobe.com,REJECT
DOMAIN-SUFFIX,coze.com,🇺🇲 美国节点
DOMAIN,bard.google.com,📢 谷歌FCM
IP-CIDR,*************/32,DIRECT,no-resolve
DOMAIN-SUFFIX,perplexity.ai,🇺🇲 美国节点
IP-CIDR,*************/32,🚀 节点选择,no-resolve
IP-CIDR,**************/32,DIRECT,no-resolve
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/LocalAreaNetwork.list,🎯 全球直连,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/UnBan.list,🎯 全球直连,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanAD.list,🛑 广告拦截,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanProgramAD.list,🍃 应用净化,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/GoogleFCM.list,📢 谷歌FCM,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/GoogleCN.list,🎯 全球直连,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/SteamCN.list,🎯 全球直连,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/OneDrive.list,Ⓜ️ 微软云盘,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Microsoft.list,Ⓜ️ 微软服务,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Apple.list,🍎 苹果服务,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Telegram.list,📲 电报消息,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/OpenAi.list,💬 OpenAi,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/NetEaseMusic.list,🎶 网易音乐,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/Epic.list,🎮 游戏平台,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/Origin.list,🎮 游戏平台,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/Sony.list,🎮 游戏平台,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/Steam.list,🎮 游戏平台,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/Nintendo.list,🎮 游戏平台,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/YouTube.list,📹 油管视频,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/Netflix.list,🎥 奈飞视频,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Disney/Disney.list,🎥 迪士尼+,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/AmazonPrimeVideo/AmazonPrimeVideo.list,🎥 PrimeVideo,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/Bahamut.list,📺 巴哈姆特,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/BilibiliHMT.list,📺 哔哩哔哩,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/Bilibili.list,📺 哔哩哔哩,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ChinaMedia.list,🌏 国内媒体,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ProxyMedia.list,🌍 国外媒体,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ProxyGFWlist.list,🚀 节点选择,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ChinaDomain.list,🎯 全球直连,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ChinaCompanyIp.list,🎯 全球直连,update-interval=86400
RULE-SET,https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Download.list,🎯 全球直连,update-interval=86400
IP-CIDR,**************/32,REJECT,no-resolve
IP-CIDR6,2402:4e00:1200:ed00:0:9089:6dac:96b6/128,REJECT,no-resolve
# > ZOL
DOMAIN,apppv.zol.com.cn,REJECT
DOMAIN,pvnapp.zol.com.cn,REJECT
PROCESS-NAME,/Library/Application Support/Adobe/ARMDC/Application/Acrobat Update Helper.app/Contents/MacOS/Acrobat Update Helper,REJECT
# Rulesets
RULE-SET,SYSTEM,DIRECT
# LAN
RULE-SET,LAN,DIRECT
# GeoIP CN
DOMAIN,************,DIRECT
DOMAIN-SUFFIX,local,DIRECT
IP-CIDR,*********/8,DIRECT,no-resolve
IP-CIDR,**************/32,🇭🇰 azure_hk,no-resolve
IP-CIDR,***************/32,DIRECT,no-resolve
SRC-IP,*************,DIRECT
GEOIP,CN,🎯 全球直连
# Final
FINAL,🐟 漏网之鱼
# FINAL,🚀 节点选择,dns-failed

[Host]
raw.githubusercontent.com = server:https://dns.cloudflare.com/dns-query
github.com = server:https://dns.cloudflare.com/dns-query

[URL Rewrite]
^https?://(www.)?g.cn https://www.google.com 302
^https?://(www.)?google.cn https://www.google.com 302

[MITM]
skip-server-cert-verify = false
tcp-connection = true
h2 = true



